# 简单的Python API示例

这是一个使用Flask构建的简单Python API，提供通过POST方法获取和添加信息的功能。

## 功能特性

- 提供用户和产品数据的查询功能
- 支持通过POST方法添加新数据
- 完整的Docker容器化支持

## API端点

### 1. 首页
- **URL**: `/`
- **方法**: GET
- **描述**: 显示API信息和可用端点

### 2. 查询数据
- **URL**: `/api/query`
- **方法**: POST
- **请求格式**:
  ```json
  {
    "type": "users",  // 或 "products"
    "id": 1          // 可选，指定要查询的ID
  }
  ```

### 3. 添加数据
- **URL**: `/api/add`
- **方法**: POST
- **请求格式**:
  ```json
  {
    "type": "users",  // 或 "products"
    "data": {
      "name": "新用户",
      "email": "<EMAIL>"
    }
  }
  ```

## 本地运行

### 直接运行
```bash
pip install -r requirements.txt
python app.py
```

### 使用Docker

1. 构建Docker镜像：
```bash
docker build -t python-api .
```

2. 运行容器：
```bash
docker run -p 8080:8080 python-api
```

**注意**: 如果遇到网络连接问题无法拉取Docker镜像，可以：
- 检查网络连接
- 使用Docker镜像代理或镜像源
- 或者直接使用本地Python环境运行

### 使用Docker Compose

```bash
docker-compose up --build
```

## 测试API

使用curl测试查询功能：
```bash
# 查询所有用户
curl -X POST http://localhost:8080/api/query \
  -H "Content-Type: application/json" \
  -d '{"type": "users"}'

# 查询特定用户
curl -X POST http://localhost:8080/api/query \
  -H "Content-Type: application/json" \
  -d '{"type": "users", "id": 1}'

# 添加新用户
curl -X POST http://localhost:8080/api/add \
  -H "Content-Type: application/json" \
  -d '{"type": "users", "data": {"name": "新用户", "email": "<EMAIL>"}}'
```

API将在 http://localhost:8080 上运行。
