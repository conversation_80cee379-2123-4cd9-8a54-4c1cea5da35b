from flask import Flask, request, jsonify
import json

app = Flask(__name__)

# 模拟数据存储
data_store = {
    "users": [
        {"id": 1, "name": "张三", "email": "<PERSON><PERSON><PERSON>@example.com"},
        {"id": 2, "name": "李四", "email": "<EMAIL>"},
        {"id": 3, "name": "王五", "email": "<EMAIL>"}
    ],
    "products": [
        {"id": 1, "name": "笔记本电脑", "price": 5999},
        {"id": 2, "name": "手机", "price": 2999},
        {"id": 3, "name": "平板电脑", "price": 1999}
    ]
}

@app.route('/', methods=['GET'])
def home():
    return jsonify({
        "message": "欢迎使用简单的Python API",
        "endpoints": {
            "POST /api/query": "查询数据",
            "POST /api/add": "添加数据"
        }
    })

@app.route('/api/query', methods=['POST'])
def query_data():
    """
    通过POST方法查询数据
    请求格式: {"type": "users" 或 "products", "id": 可选的ID}
    """
    try:
        request_data = request.get_json()
        
        if not request_data:
            return jsonify({"error": "请提供JSON数据"}), 400
        
        data_type = request_data.get('type')
        item_id = request_data.get('id')
        
        if data_type not in data_store:
            return jsonify({"error": f"不支持的数据类型: {data_type}"}), 400
        
        # 如果指定了ID，返回特定项目
        if item_id:
            items = [item for item in data_store[data_type] if item['id'] == item_id]
            if items:
                return jsonify({"data": items[0], "count": 1})
            else:
                return jsonify({"error": f"未找到ID为{item_id}的{data_type}"}), 404
        
        # 返回所有项目
        return jsonify({"data": data_store[data_type], "count": len(data_store[data_type])})
        
    except Exception as e:
        return jsonify({"error": f"处理请求时出错: {str(e)}"}), 500

@app.route('/api/add', methods=['POST'])
def add_data():
    """
    通过POST方法添加数据
    请求格式: {"type": "users" 或 "products", "data": {...}}
    """
    try:
        request_data = request.get_json()
        
        if not request_data:
            return jsonify({"error": "请提供JSON数据"}), 400
        
        data_type = request_data.get('type')
        new_data = request_data.get('data')
        
        if data_type not in data_store:
            return jsonify({"error": f"不支持的数据类型: {data_type}"}), 400
        
        if not new_data:
            return jsonify({"error": "请提供要添加的数据"}), 400
        
        # 生成新的ID
        max_id = max([item['id'] for item in data_store[data_type]], default=0)
        new_data['id'] = max_id + 1
        
        # 添加数据
        data_store[data_type].append(new_data)
        
        return jsonify({
            "message": f"成功添加{data_type}数据",
            "data": new_data
        }), 201
        
    except Exception as e:
        return jsonify({"error": f"处理请求时出错: {str(e)}"}), 500

if __name__ == '__main__':
    # 仅用于本地开发测试
    app.run(host='0.0.0.0', port=8080, debug=True)
